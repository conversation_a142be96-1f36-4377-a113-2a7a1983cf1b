import { CDPopover } from '@tencent/cocraft-react-components';
import React, { useState, useCallback, useMemo, useRef } from 'react';
import { useInjectService } from '~workspace/hooks/useInjectService';
import { AiAssistantService } from '~workspace/services/ai-assistant-services';
import { HTML2DESIGN_DIALOG_HEIGHT, HTML2DESIGN_DIALOG_WIDTH } from './constant';
import {
  cn,
} from '@tencent/cocraft-react-components/components/_utils/cn';
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from '@tencent/cocraft-react-components/components/ui/tabs';
import {
  Button,
} from '@tencent/cocraft-react-components/components/ui/button';

import LoadingProgress from './h2d/loading-progress';
import PluginImportDesign from './h2d/plugin-import-design';
import UrlImportDesign from './h2d/url-import-design';
import i18n from '~workspace/i18n';
import SizeSelector, { SIZE_OPTIONS } from '~workspace/components/AiAssistant/h2d/size-selector';
import SettingsPanel, { PREFERENCE_OPTIONS, THEME_OPTIONS } from '~workspace/components/AiAssistant/h2d/settings-panel';
import { useSizeManager } from '~workspace/components/AiAssistant/hooks/use-h2d-size-manager';
import { useProgressManager } from '~workspace/components/AiAssistant/hooks/use-h2d-process-manager';
import { H2dLoadingMaskService } from '~workspace/services/h2d/loading-mask';


export default function Html2DesignDialog() {
  const aiAssistantService = useInjectService<AiAssistantService>(AiAssistantService);
  const h2dLoadingMaskServer = useInjectService<H2dLoadingMaskService>(H2dLoadingMaskService);

  // 基础状态
  const [visible, setVisible] = useState(true);
  const [activeTab, setActiveTab] = useState('url');
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [theme, setTheme] = useState('light');
  const [preference, setPreference] = useState(['1']);
  const [pluginDataUploaded, setPluginDataUploaded] = useState(false);
  const [pluginUrl, setPluginUrl] = useState('');

  // 组件引用
  const urlImportRef = useRef<{ handleStartImport: () => Promise<void> }>(null);
  const pluginImportRef = useRef<{ handleStartImport: () => Promise<void> }>(null);

  // 自定义Hooks
  const { loadingSteps, resetProgress, updateProgress } = useProgressManager();
  const { sizeRows, addSizeRow, removeSizeRow, updateSizeRow } = useSizeManager();

  // 事件处理函数
  const handleClose = useCallback(() => {
    setVisible(false);
    aiAssistantService.closeHtml2DesignDialog();
  }, [aiAssistantService]);

  const handleUrlChange = useCallback((value: string) => {
    setUrl(value);
  }, []);

  const handlePluginFileUpload = useCallback((data: any) => {
    setPluginDataUploaded(!!data);
    // 从插件数据中提取 URL 用于显示
    if (data && data.length > 0 && data[0].doc?.baseURI) {
      setPluginUrl(data[0].doc.baseURI);
    }
  }, []);

  // 处理开始导入的回调
  const handleStartImportCallback = useCallback(() => {
    setIsLoading(true);
    resetProgress();
  }, [resetProgress]);

  // 处理导入完成的回调
  const handleImportCompleteCallback = useCallback(() => {
    setIsLoading(false);
    // 重置插件导入相关状态
    setPluginDataUploaded(false);
    setPluginUrl('');
  }, []);

  // 处理导入错误的回调
  const handleImportErrorCallback = useCallback((error: Error) => {
    console.error('Import failed:', error);
    setIsLoading(false);
  }, []);

  // 主要的开始导入处理函数
  const handleStartImport = useCallback(async () => {
    if (activeTab === 'url' && urlImportRef.current) {
      await urlImportRef.current.handleStartImport();
    } else if (activeTab === 'plugin' && pluginImportRef.current) {
      await pluginImportRef.current.handleStartImport();
    }
  }, [activeTab]);

  const handleStopImport = useCallback(() => {
    setIsLoading(false);
    resetProgress();
  }, [resetProgress]);

  // 计算显示值
  const displayValues = useMemo(() => ({
    sizes: sizeRows.map(row => SIZE_OPTIONS.find(opt => opt.value === row.value)?.label || row.value.toString()).join(', '),
    theme: THEME_OPTIONS.find(item => item.value === theme)?.label || '浅色',
    preference: preference
      .map(p => PREFERENCE_OPTIONS.find(item => item.value === p)?.label)
      .filter(Boolean)
      .join(', '),
  }), [sizeRows, theme, preference]);

  // 头部组件
  const header = useMemo(() => (
    <Tabs
      className="flex flex-col flex-1"
      value={activeTab}
      onValueChange={setActiveTab}
    >
      <TabsList className="h-[28px] w-[173px] flex mr-2">
        <TabsTrigger value="url" className="flex-2 rounded-2xl">
          {i18n.t('component.h2d.ripe-lands-yawn')}
        </TabsTrigger>
        <TabsTrigger value="plugin" className="flex-2 rounded-2xl p-[11px]">
          {i18n.t('component.h2d.orange-eggs-visit')}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  ), [activeTab]);

  return (
    <CDPopover
      width={HTML2DESIGN_DIALOG_WIDTH}
      trigger={<div className="h-[1px] assistant-trigger"></div>}
      placement="top-left"
      visible={visible}
      onClose={handleClose}
      header={header}
      ignoreDragElements={() => []}
    >
      <Button onClick={() => {
        h2dLoadingMaskServer.create([{ name: '1', type: 'app' }, { name: '2', type: 'app' }, { name: '3', type: 'app' }]);
      }}></Button>
      <div style={{ height: HTML2DESIGN_DIALOG_HEIGHT }} className={cn('flex flex-col gap-2 p-2')}>
        {isLoading ? (
          <LoadingProgress
            steps={loadingSteps}
            currentUrl={activeTab === 'url' ? url : pluginUrl}
            currentSize={displayValues.sizes}
            currentTheme={displayValues.theme}
            currentPreference={displayValues.preference}
            onStop={handleStopImport}
          />
        ) : (
          <>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
              <TabsContent value="url" className="mt-0">
                <UrlImportDesign
                  ref={urlImportRef}
                  value={url}
                  onChange={handleUrlChange}
                  sizeRows={sizeRows}
                  theme={theme}
                  onProgressUpdate={updateProgress}
                  onStartImport={handleStartImportCallback}
                  onImportComplete={handleImportCompleteCallback}
                  onImportError={handleImportErrorCallback}
                />
              </TabsContent>
              <TabsContent value="plugin" className="mt-0">
                <PluginImportDesign
                  ref={pluginImportRef}
                  sizeRows={sizeRows}
                  theme={theme}
                  onProgressUpdate={updateProgress}
                  onStartImport={handleStartImportCallback}
                  onImportComplete={handleImportCompleteCallback}
                  onImportError={handleImportErrorCallback}
                  onFileUpload={handlePluginFileUpload}
                />
              </TabsContent>
            </Tabs>

            <hr className="mx-[-8px] w-[calc(100%+16px)] border-t border-gray-200" />

            <SizeSelector
              sizeRows={sizeRows}
              onAdd={addSizeRow}
              onRemove={removeSizeRow}
              onChange={updateSizeRow}
            />

            <hr className="mx-[-8px] w-[calc(100%+16px)] border-t border-gray-200" />

            <SettingsPanel
              theme={theme}
              preference={preference}
              onThemeChange={setTheme}
              onPreferenceChange={setPreference}
            />

            <Button
              variant="primary"
              shape="round"
              className="min-w-[48px] w-[69px] self-end mt-3"
              onClick={handleStartImport}
              disabled={activeTab === 'url' ? !url.trim() : !pluginDataUploaded}
            >
              {i18n.t('component.h2d.angry-garlics-relax')}
            </Button>
          </>
        )}
      </div>
    </CDPopover>
  );
}
